<?php
if (!isset($_SESSION['userData'])){
	header("Location:".$Site_URL.'/admincp/login.php'); exit();
}
 
?>
<div class="left-sidenav">
	<!-- LOGO -->
	<div class="topbar-left">
		<a href="index.php" class="logo">
		 
		<span>
			<img style="height: 50px;background: #fff;padding: 1px;border-radius: 5px;" src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ; ?>" alt="logo-large" class="logo-lg logo-light"> 
			<img style="height: 50px;background: #fff;padding: 1px;border-radius: 5px;" src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ; ?>" class="logo-lg logo-dark">
		</span>
		</a>
	</div>
	<!--end logo-->
	<div class="leftbar-profile p-3 w-100">
	<div class="media position-relative">
		<div class="leftbar-user online"><img src="<?php echo $Site_URL.'/admincp/img/user.png';?>" alt="" class="thumb-md rounded-circle"></div>
		<div class="media-body align-self-center text-truncate ml-3">
			<h5 class="mt-0 mb-1 font-weight-semibold"><?php echo $_SESSION['userData']; ?></h5>
			<p class="text-muted text-uppercase mb-0 font-12">مدير عام</p>
		</div>
		<!--end media-body-->
	</div>
	</div>
	<ul class="metismenu left-sidenav-menu slimscroll">

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'لوحة الإدارة'){echo 'mm-active';}?>">
			<a href="index.php" class="menu-link"><i class="mdi mdi-home align-self-center vertical-menu-icon icon-dual-vertical"></i> <span>الرئيسية</span></a>
		</li>

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'الأوردرات'){echo 'mm-active';}?>">
			<a href="orders.php" class="menu-link"><i class="mdi mdi-library-books vertical-menu-icon icon-dual-vertical"></i> <span>الأوردرات</span></a>
		</li>

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'الحملات'){echo 'mm-active';}?>">
			<a href="offers.php" class="menu-link"><i class="mdi mdi-bus vertical-menu-icon icon-dual-vertical"></i> <span>الحملات</span></a>
		</li>

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'الفنادق'){echo 'mm-active';}?>">
			<a href="hotels.php" class="menu-link"><i class="mdi mdi-hotel vertical-menu-icon icon-dual-vertical"></i> <span>الفنادق</span></a>
		</li>

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'المحافظات'){echo 'mm-active';}?>">
			<a href="cities.php" class="menu-link"><i class="mdi mdi-map-marker-radius vertical-menu-icon icon-dual-vertical"></i> <span>المحافظات</span></a>
		</li>

		<li class="leftbar-menu-item <?php if (isset($Title_page) && in_array($Title_page, ['محطات الباصات', 'رحلات الباصات', 'حجوزات الباصات'])){echo 'mm-active';}?>">
			<a href="javascript: void(0);" class="menu-link has-arrow"><i class="mdi mdi-bus vertical-menu-icon icon-dual-vertical"></i> <span>إدارة الباصات</span></a>
			<ul class="nav-second-level" aria-expanded="false">
				<li class="nav-item"><a class="nav-link <?php if (isset($Title_page) && $Title_page == 'محطات الباصات'){echo 'mm-active';}?>" href="bus-stations.php"><i class="mdi mdi-map-marker"></i>محطات الباصات</a></li>
				<li class="nav-item"><a class="nav-link <?php if (isset($Title_page) && $Title_page == 'رحلات الباصات'){echo 'mm-active';}?>" href="bus-trips.php"><i class="mdi mdi-bus-clock"></i>رحلات الباصات</a></li>
				<li class="nav-item"><a class="nav-link <?php if (isset($Title_page) && $Title_page == 'حجوزات الباصات'){echo 'mm-active';}?>" href="bus-bookings.php"><i class="mdi mdi-book-open-page-variant"></i>حجوزات الباصات</a></li>
			</ul>
		</li>
		
		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'مكتبة الصور'){echo 'mm-active';}?>">
			<a href="photos.php" class="menu-link"><i class="mdi  mdi-panorama vertical-menu-icon icon-dual-vertical"></i> <span>مكتبة الصور</span></a>
		</li>
 

		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'رسائل اتصل بنا'){echo 'mm-active';}?>">
			<a href="contactus.php" class="menu-link"><i class="mdi mdi-facebook-messenger vertical-menu-icon icon-dual-vertical"></i> <span>رسائل اتصل بنا</span></a>
		</li>

		 
		<li class="leftbar-menu-item <?php if (isset($Title_page) && $Title_page == 'الإعدادات'){echo 'mm-active';}?>">
			<a href="settings.php" class="menu-link"><i class="mdi mdi-settings-outline align-self-center vertical-menu-icon icon-dual-vertical"></i> <span>الإعدادات</span></a>
		</li>
		
		<li class="leftbar-menu-item">
			<a href="<?php echo $Site_URL;?>" target="_blank" class="menu-link"><i class="mdi mdi-backup-restore align-self-center vertical-menu-icon icon-dual-vertical"></i> <span>الرجوع الى الموقع</span></a>
		</li>

		<li class="leftbar-menu-item">
			<a href="logout.php" class="menu-link"><i class="mdi mdi-logout-variant align-self-center vertical-menu-icon icon-dual-vertical"></i> <span>تسجيل الخروج</span></a>
		</li>


	</ul>
</div>
<!-- end left-sidenav--><!-- Top Bar Start -->
<div class="topbar">
	<!-- Navbar -->
	<nav class="navbar-custom">
	<ul class="list-unstyled topbar-nav float-right mb-0">
		
		<li class="dropdown">
			<a class="nav-link dropdown-toggle waves-effect waves-light nav-user" data-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false"><img src="<?php echo $Site_URL.'/admincp/img/user.png';?>" alt="profile-user" class="rounded-circle"> <span class="ml-1 nav-user-name hidden-sm"><?php echo $_SESSION['userData'] ; ?> <i class="mdi mdi-chevron-down"></i></span></a>
			<div class="dropdown-menu dropdown-menu-right">
				
				<a class="dropdown-item" href="logout.php"><i class="dripicons-exit text-muted mr-2"></i> الخروج</a>
			</div>
		</li>
		
	</ul>
	<!--end topbar-nav-->
	<ul class="list-unstyled topbar-nav mb-0">
		<li><a href="index.php"><span class="responsive-logo"><img style="height: 50px;background: #fff;padding: 1px;border-radius: 5px;" src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ; ?>" alt="logo-small" class="logo-sm align-self-center" height="34"></span></a></li>
		<li><button class="button-menu-mobile nav-link"><i data-feather="menu" class="align-self-center"></i></button></li>
		
	</ul>
	</nav>
	<!-- end navbar-->
</div>
<!-- Top Bar End -->

<div class="page-wrapper">
	<!-- Page Content-->
	<div class="page-content-tab">
		<div class="container-fluid">
		<?php 
		if(isset($Title_page) && $Title_page != ''){
		?>
		<div class="row">
			<div class="col-sm-12">
				<div class="page-title-box">
				<h4 class="page-title"><?php echo $Title_page;?></h4>
				</div>
			</div>
			<!--end col-->
		</div>
		<?php
		}
		?>