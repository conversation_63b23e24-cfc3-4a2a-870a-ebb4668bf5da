<?php
include('header.php');
include('navbar.php');

// التحقق من البيانات المرسلة
if (!isset($_POST['trip_type']) || !isset($_POST['city_id']) || !isset($_POST['station_id']) || !isset($_POST['travel_date']) || !isset($_POST['seats_count'])) {
    header('Location: bus-booking.php');
    exit;
}

$trip_type = $_POST['trip_type'];
$city_id = intval($_POST['city_id']);
$station_id = intval($_POST['station_id']);
$travel_date = $_POST['travel_date'];
$seats_count = intval($_POST['seats_count']);

// التحقق من صحة التاريخ
$selected_date = new DateTime($travel_date);
$today = new DateTime();
if ($selected_date < $today) {
    header('Location: bus-booking.php');
    exit;
}

// جلب بيانات المحطة المحددة
$station_data = getAllFrom('*', 'bus_stations', 'WHERE id = '.$station_id, '')[0] ?? null;
if (!$station_data) {
    header('Location: bus-booking.php');
    exit;
}

// تحديد محطات الوصول حسب نوع الرحلة
if ($trip_type == 'to_makkah') {
    $departure_station_id = $station_id;
    $arrival_stations = getAllFrom('*', 'bus_stations', 'WHERE city_name = "مكة المكرمة" AND status = 1', 'ORDER BY station_name');
} else {
    $departure_station_id = $station_id;
    $arrival_stations = getAllFrom('*', 'bus_stations', 'WHERE city_id = '.$city_id.' AND status = 1', 'ORDER BY station_name');
}

// جلب الرحلات المتاحة
$day_of_week = $selected_date->format('w') + 1; // تحويل إلى نظام الأرقام المستخدم (1=الأحد)
if ($day_of_week == 8) $day_of_week = 1;

$trips_query = "SELECT bt.*, 
                ds.station_name as departure_station_name, ds.city_name as departure_city_name,
                as_table.station_name as arrival_station_name, as_table.city_name as arrival_city_name
                FROM bus_trips bt 
                JOIN bus_stations ds ON bt.departure_station_id = ds.id 
                JOIN bus_stations as_table ON bt.arrival_station_id = as_table.id 
                WHERE bt.trip_type = '".$trip_type."' 
                AND bt.departure_station_id = ".$departure_station_id." 
                AND bt.status = 1 
                AND FIND_IN_SET('".$day_of_week."', bt.available_days) > 0
                ORDER BY bt.departure_time";

$available_trips = getAllFrom('*', '('.$trips_query.') as trips_data', '', '');

// إنشاء تواريخ إضافية (4 أيام تالية)
$additional_dates = [];
for ($i = 1; $i <= 4; $i++) {
    $next_date = clone $selected_date;
    $next_date->add(new DateInterval('P'.$i.'D'));
    $additional_dates[] = $next_date;
}

// حفظ بيانات البحث في الجلسة
$_SESSION['bus_search'] = [
    'trip_type' => $trip_type,
    'city_id' => $city_id,
    'station_id' => $station_id,
    'travel_date' => $travel_date,
    'seats_count' => $seats_count,
    'station_data' => $station_data
];
?>

<style>
.search-results-section {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 40px 0;
}

.search-summary {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.date-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.date-tab {
    flex: 0 0 auto;
    padding: 15px 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #495057;
    min-width: 120px;
}

.date-tab.active {
    background: #ff8c00;
    border-color: #ff8c00;
    color: white;
}

.date-tab:hover {
    border-color: #ff8c00;
    transform: translateY(-2px);
}

.trip-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.trip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.trip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.trip-time {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff8c00;
}

.trip-duration {
    color: #6c757d;
    font-size: 0.9rem;
}

.trip-route {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.route-point {
    text-align: center;
    flex: 1;
}

.route-arrow {
    color: #ff8c00;
    font-size: 1.5rem;
}

.trip-price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #28a745;
}

.select-trip-btn {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.select-trip-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

.no-trips {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    color: #6c757d;
}

@media (max-width: 768px) {
    .trip-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .trip-route {
        flex-direction: column;
        gap: 10px;
    }
    
    .route-arrow {
        transform: rotate(90deg);
    }
}
</style>

<section class="search-results-section">
    <div class="container">
        <!-- ملخص البحث -->
        <div class="search-summary">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4 class="mb-2">
                        <?= $trip_type == 'to_makkah' ? 'رحلات الذهاب إلى مكة المكرمة' : 'رحلات العودة من مكة المكرمة' ?>
                    </h4>
                    <p class="mb-0 text-muted">
                        من: <?= $station_data['station_name'] ?> - <?= $station_data['city_name'] ?> | 
                        التاريخ: <?= date('d/m/Y', strtotime($travel_date)) ?> | 
                        عدد المسافرين: <?= $seats_count ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="bus-booking.php" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> تعديل البحث
                    </a>
                </div>
            </div>
        </div>

        <!-- تبويبات التواريخ -->
        <div class="date-tabs">
            <a href="#" class="date-tab active" data-date="<?= $travel_date ?>">
                <div class="fw-bold"><?= date('d/m', strtotime($travel_date)) ?></div>
                <div class="small"><?= date('D', strtotime($travel_date)) ?></div>
            </a>
            <?php foreach ($additional_dates as $date): ?>
                <a href="?date=<?= $date->format('Y-m-d') ?>" class="date-tab" data-date="<?= $date->format('Y-m-d') ?>">
                    <div class="fw-bold"><?= $date->format('d/m') ?></div>
                    <div class="small"><?= $date->format('D') ?></div>
                </a>
            <?php endforeach; ?>
        </div>

        <!-- الرحلات المتاحة -->
        <?php if (count($available_trips) > 0): ?>
            <?php foreach ($available_trips as $trip): ?>
                <div class="trip-card">
                    <div class="trip-header">
                        <div>
                            <div class="trip-time"><?= date('H:i', strtotime($trip['departure_time'])) ?></div>
                            <div class="trip-duration">موعد الانطلاق</div>
                        </div>
                        <div>
                            <div class="trip-time"><?= date('H:i', strtotime($trip['arrival_time'])) ?></div>
                            <div class="trip-duration">موعد الوصول</div>
                        </div>
                        <div class="text-end">
                            <div class="trip-price"><?= number_format($trip['seat_price'] * $seats_count, 2) ?> ريال</div>
                            <div class="small text-muted">للمجموع</div>
                        </div>
                    </div>

                    <div class="trip-route">
                        <div class="route-point">
                            <div class="fw-bold"><?= $trip['departure_station_name'] ?></div>
                            <div class="small text-muted"><?= $trip['departure_city_name'] ?></div>
                        </div>
                        <div class="route-arrow">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="route-point">
                            <div class="fw-bold"><?= $trip['arrival_station_name'] ?></div>
                            <div class="small text-muted"><?= $trip['arrival_city_name'] ?></div>
                        </div>
                    </div>

                    <div class="text-end">
                        <form method="POST" action="bus-booking-form.php" style="display: inline;">
                            <input type="hidden" name="trip_id" value="<?= $trip['id'] ?>">
                            <input type="hidden" name="travel_date" value="<?= $travel_date ?>">
                            <input type="hidden" name="seats_count" value="<?= $seats_count ?>">
                            <button type="submit" class="select-trip-btn">
                                <i class="fas fa-check"></i> اختر هذه الرحلة
                            </button>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="no-trips">
                <i class="fas fa-bus fa-3x mb-3 text-muted"></i>
                <h4>لا توجد رحلات متاحة</h4>
                <p>عذراً، لا توجد رحلات متاحة في التاريخ المحدد. يرجى اختيار تاريخ آخر.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include('footer.php'); ?>
