<?php
include('header.php');
include('navbar.php');

// التحقق من البيانات المرسلة
if (!isset($_POST['trip_id']) || !isset($_POST['travel_date']) || !isset($_POST['seats_count'])) {
    header('Location: bus-booking.php');
    exit;
}

$trip_id = intval($_POST['trip_id']);
$travel_date = $_POST['travel_date'];
$seats_count = intval($_POST['seats_count']);

// جلب بيانات الرحلة
$trip_query = "SELECT bt.*, 
               ds.station_name as departure_station_name, ds.city_name as departure_city_name,
               as_table.station_name as arrival_station_name, as_table.city_name as arrival_city_name
               FROM bus_trips bt 
               JOIN bus_stations ds ON bt.departure_station_id = ds.id 
               JOIN bus_stations as_table ON bt.arrival_station_id = as_table.id 
               WHERE bt.id = ".$trip_id;

$trip_data = getAllFrom('*', '('.$trip_query.') as trip_info', '', '')[0] ?? null;

if (!$trip_data) {
    header('Location: bus-booking.php');
    exit;
}

// حساب السعر الإجمالي
$total_price = $trip_data['seat_price'] * $seats_count;

// حفظ بيانات الحجز في الجلسة
$_SESSION['bus_booking'] = [
    'trip_id' => $trip_id,
    'travel_date' => $travel_date,
    'seats_count' => $seats_count,
    'trip_data' => $trip_data,
    'total_price' => $total_price
];
?>

<style>
.booking-form-section {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 40px 0;
}

.booking-container {
    max-width: 800px;
    margin: 0 auto;
}

.trip-summary {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.booking-form-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.form-section {
    margin-bottom: 30px;
}

.section-title {
    color: #ff8c00;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ff8c00;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #ff8c00;
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
}

.price-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.total-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #28a745;
    border-top: 2px solid #dee2e6;
    padding-top: 15px;
    margin-top: 15px;
}

.submit-btn {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    color: white;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 140, 0, 0.4);
}

.route-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 15px 0;
}

.route-point {
    text-align: center;
    flex: 1;
}

.route-arrow {
    color: #ff8c00;
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .route-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .route-arrow {
        transform: rotate(90deg);
    }
    
    .booking-container {
        margin: 0 15px;
    }
}
</style>

<section class="booking-form-section">
    <div class="container">
        <div class="booking-container">
            <!-- ملخص الرحلة -->
            <div class="trip-summary">
                <h4 class="mb-3">ملخص الرحلة</h4>
                
                <div class="route-info">
                    <div class="route-point">
                        <div class="fw-bold"><?= $trip_data['departure_station_name'] ?></div>
                        <div class="text-muted"><?= $trip_data['departure_city_name'] ?></div>
                        <div class="text-primary"><?= date('H:i', strtotime($trip_data['departure_time'])) ?></div>
                    </div>
                    <div class="route-arrow">
                        <i class="fas fa-arrow-left"></i>
                    </div>
                    <div class="route-point">
                        <div class="fw-bold"><?= $trip_data['arrival_station_name'] ?></div>
                        <div class="text-muted"><?= $trip_data['arrival_city_name'] ?></div>
                        <div class="text-primary"><?= date('H:i', strtotime($trip_data['arrival_time'])) ?></div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-4">
                        <strong>تاريخ السفر:</strong><br>
                        <?= date('d/m/Y', strtotime($travel_date)) ?>
                    </div>
                    <div class="col-md-4">
                        <strong>عدد المسافرين:</strong><br>
                        <?= $seats_count ?> <?= $seats_count == 1 ? 'مسافر' : 'مسافرين' ?>
                    </div>
                    <div class="col-md-4">
                        <strong>السعر الإجمالي:</strong><br>
                        <span class="text-success fw-bold"><?= number_format($total_price, 2) ?> ريال</span>
                    </div>
                </div>
            </div>

            <!-- نموذج بيانات المسافر -->
            <div class="booking-form-card">
                <form id="busBookingForm" method="POST" action="process-bus-booking.php">
                    <div class="form-section">
                        <h5 class="section-title">بيانات المسافر الرئيسي</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" name="passenger_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">رقم الجوال *</label>
                                    <input type="tel" class="form-control" name="passenger_phone" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="passenger_email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">رقم الهوية/الإقامة</label>
                                    <input type="text" class="form-control" name="passenger_id_number">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">ملاحظات خاصة</label>
                            <textarea class="form-control" name="special_requests" rows="3" placeholder="أي طلبات خاصة أو ملاحظات..."></textarea>
                        </div>
                    </div>

                    <!-- ملخص الأسعار -->
                    <div class="price-summary">
                        <h6 class="mb-3">ملخص الأسعار</h6>
                        <div class="price-row">
                            <span>سعر المقعد الواحد:</span>
                            <span><?= number_format($trip_data['seat_price'], 2) ?> ريال</span>
                        </div>
                        <div class="price-row">
                            <span>عدد المقاعد:</span>
                            <span><?= $seats_count ?></span>
                        </div>
                        <div class="price-row total-price">
                            <span>المجموع الكلي:</span>
                            <span><?= number_format($total_price, 2) ?> ريال</span>
                        </div>
                    </div>

                    <!-- شروط وأحكام -->
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" name="accept_terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="terms-and-conditions.php" target="_blank">الشروط والأحكام</a> *
                            </label>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-check"></i>
                            تأكيد الحجز
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById('busBookingForm').addEventListener('submit', function(e) {
    const phone = document.querySelector('input[name="passenger_phone"]').value;
    const phoneRegex = /^[0-9]{10}$/;
    
    if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
        e.preventDefault();
        alert('يرجى إدخال رقم جوال صحيح (10 أرقام)');
        return false;
    }
});
</script>

<?php include('footer.php'); ?>
