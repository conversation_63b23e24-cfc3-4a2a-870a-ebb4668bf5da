<?php
include('header.php');
include('navbar.php');

// جلب المدن والمحطات
$cities = getAllFrom('DISTINCT city_id, city_name', 'bus_stations', 'WHERE status = 1', 'ORDER BY city_name');
?>

<style>
.bus-booking-section {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.bus-booking-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('img/bus-1.jpeg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.booking-container {
    position: relative;
    z-index: 2;
    padding: 80px 0;
}

.booking-form-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title {
    color: white;
    font-size: 3rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 50px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.trip-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: center;
}

.trip-type-option {
    flex: 1;
    max-width: 200px;
}

.trip-type-option input[type="radio"] {
    display: none;
}

.trip-type-option label {
    display: block;
    padding: 15px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #495057;
}

.trip-type-option input[type="radio"]:checked + label {
    background: #ff8c00;
    border-color: #ff8c00;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.3);
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #ff8c00;
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
}

.search-btn {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    color: white;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 140, 0, 0.4);
    background: linear-gradient(135deg, #ff6b00 0%, #ff5500 100%);
}

.bus-image {
    position: absolute;
    right: -100px;
    bottom: 0;
    width: 600px;
    opacity: 0.8;
    z-index: 1;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .trip-type-selector {
        flex-direction: column;
        gap: 10px;
    }
    
    .bus-image {
        display: none;
    }
    
    .booking-form-card {
        padding: 20px;
        margin: 20px;
    }
}
</style>

<section class="bus-booking-section">
    <div class="container booking-container">
        <h1 class="page-title">باصات من وإلى مكة يوميا</h1>
        
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="booking-form-card">
                    <form id="busSearchForm" method="POST" action="bus-search-results.php">
                        <!-- نوع الرحلة -->
                        <div class="trip-type-selector">
                            <div class="trip-type-option">
                                <input type="radio" id="to_makkah" name="trip_type" value="to_makkah" checked>
                                <label for="to_makkah">
                                    <i class="fas fa-arrow-left"></i>
                                    ذهاب إلى مكة
                                </label>
                            </div>
                            <div class="trip-type-option">
                                <input type="radio" id="from_makkah" name="trip_type" value="from_makkah">
                                <label for="from_makkah">
                                    <i class="fas fa-arrow-right"></i>
                                    عودة من مكة
                                </label>
                            </div>
                        </div>

                        <div class="row">
                            <!-- المدينة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">السفر من المدينة</label>
                                    <select class="form-select" id="city_select" name="city_id" required>
                                        <option value="">اختر المدينة</option>
                                        <?php foreach($cities as $city): ?>
                                            <option value="<?= $city['city_id'] ?>"><?= $city['city_name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- المحطة -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">السفر من المحطة</label>
                                    <select class="form-select" id="station_select" name="station_id" required>
                                        <option value="">اختر المحطة</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ السفر -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">تاريخ السفر</label>
                                    <input type="date" class="form-control" name="travel_date" 
                                           min="<?= date('Y-m-d') ?>" 
                                           max="<?= date('Y-m-d', strtotime('+3 months')) ?>" required>
                                </div>
                            </div>

                            <!-- عدد المقاعد -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">عدد المسافرين</label>
                                    <select class="form-select" name="seats_count" required>
                                        <?php for($i = 1; $i <= 10; $i++): ?>
                                            <option value="<?= $i ?>"><?= $i ?> <?= $i == 1 ? 'مسافر' : 'مسافرين' ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                                أعرض الرحلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <img src="img/bus-1.jpeg" alt="Bus" class="bus-image">
</section>

<script>
// تحديث المحطات عند تغيير المدينة
document.getElementById('city_select').addEventListener('change', function() {
    const cityId = this.value;
    const stationSelect = document.getElementById('station_select');
    const tripType = document.querySelector('input[name="trip_type"]:checked').value;
    
    // مسح المحطات الحالية
    stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
    
    if (cityId) {
        // جلب المحطات للمدينة المحددة
        fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_stations&city_id=${cityId}&trip_type=${tripType}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.stations.forEach(station => {
                    const option = document.createElement('option');
                    option.value = station.id;
                    option.textContent = station.station_name;
                    stationSelect.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error:', error));
    }
});

// تحديث المحطات عند تغيير نوع الرحلة
document.querySelectorAll('input[name="trip_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const citySelect = document.getElementById('city_select');
        if (citySelect.value) {
            citySelect.dispatchEvent(new Event('change'));
        }
    });
});
</script>

<?php include('footer.php'); ?>
