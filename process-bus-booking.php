<?php
include('webset.php');
session_start();

// التحقق من وجود بيانات الحجز في الجلسة
if (!isset($_SESSION['bus_booking'])) {
    header('Location: bus-booking.php');
    exit;
}

// التحقق من البيانات المرسلة
if (!isset($_POST['passenger_name']) || !isset($_POST['passenger_phone']) || !isset($_POST['accept_terms'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking_data = $_SESSION['bus_booking'];
$passenger_name = trim($_POST['passenger_name']);
$passenger_phone = trim($_POST['passenger_phone']);
$passenger_email = trim($_POST['passenger_email']) ?: null;
$passenger_id_number = trim($_POST['passenger_id_number']) ?: null;
$special_requests = trim($_POST['special_requests']) ?: null;

// التحقق من صحة البيانات
if (empty($passenger_name) || empty($passenger_phone)) {
    $_SESSION['error_message'] = 'يرجى إدخال جميع البيانات المطلوبة';
    header('Location: bus-booking-form.php');
    exit;
}

// التحقق من صحة رقم الجوال
if (!preg_match('/^[0-9]{10}$/', str_replace(' ', '', $passenger_phone))) {
    $_SESSION['error_message'] = 'يرجى إدخال رقم جوال صحيح';
    header('Location: bus-booking-form.php');
    exit;
}

try {
    // بدء المعاملة
    $db->beginTransaction();
    
    // التحقق من توفر المقاعد
    $trip_date = $booking_data['travel_date'];
    $trip_id = $booking_data['trip_id'];
    $seats_count = $booking_data['seats_count'];
    
    // حساب المقاعد المحجوزة في نفس الرحلة والتاريخ
    $booked_seats_query = "SELECT COALESCE(SUM(seats_count), 0) as total_booked 
                          FROM bus_bookings 
                          WHERE trip_id = ? AND trip_date = ? 
                          AND booking_status IN ('pending', 'confirmed')";
    
    $stmt = $db->prepare($booked_seats_query);
    $stmt->execute([$trip_id, $trip_date]);
    $booked_seats = $stmt->fetch(PDO::FETCH_ASSOC)['total_booked'];
    
    // جلب العدد الكلي للمقاعد في الرحلة
    $trip_info = getAllFrom('total_seats', 'bus_trips', 'WHERE id = '.$trip_id, '')[0];
    $total_seats = $trip_info['total_seats'];
    
    // التحقق من توفر المقاعد
    if (($booked_seats + $seats_count) > $total_seats) {
        throw new Exception('عذراً، لا توجد مقاعد كافية متاحة في هذه الرحلة');
    }
    
    // إنشاء رقم مرجعي للحجز
    $booking_reference = 'BUS' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // التأكد من عدم تكرار الرقم المرجعي
    $check_ref = getAllFrom('id', 'bus_bookings', 'WHERE booking_reference = "'.$booking_reference.'"', '');
    while (count($check_ref) > 0) {
        $booking_reference = 'BUS' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $check_ref = getAllFrom('id', 'bus_bookings', 'WHERE booking_reference = "'.$booking_reference.'"', '');
    }
    
    // إدراج الحجز في قاعدة البيانات
    $insert_query = "INSERT INTO bus_bookings (
        booking_reference, trip_id, trip_date, passenger_name, passenger_phone, 
        passenger_email, passenger_id_number, seats_count, total_price, 
        booking_status, payment_status, special_requests, created_by_ip
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?, ?)";
    
    $stmt = $db->prepare($insert_query);
    $result = $stmt->execute([
        $booking_reference,
        $trip_id,
        $trip_date,
        $passenger_name,
        $passenger_phone,
        $passenger_email,
        $passenger_id_number,
        $seats_count,
        $booking_data['total_price'],
        $special_requests,
        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    if (!$result) {
        throw new Exception('فشل في حفظ الحجز');
    }
    
    // الحصول على معرف الحجز
    $booking_id = $db->lastInsertId();
    
    // تأكيد المعاملة
    $db->commit();
    
    // حفظ بيانات الحجز المكتمل في الجلسة
    $_SESSION['completed_booking'] = [
        'booking_id' => $booking_id,
        'booking_reference' => $booking_reference,
        'passenger_name' => $passenger_name,
        'passenger_phone' => $passenger_phone,
        'trip_data' => $booking_data['trip_data'],
        'travel_date' => $trip_date,
        'seats_count' => $seats_count,
        'total_price' => $booking_data['total_price']
    ];
    
    // مسح بيانات الحجز المؤقتة
    unset($_SESSION['bus_booking']);
    
    // إعادة التوجيه إلى صفحة تأكيد الحجز
    header('Location: bus-booking-confirmation.php');
    exit;
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $db->rollback();
    
    $_SESSION['error_message'] = $e->getMessage();
    header('Location: bus-booking-form.php');
    exit;
}
?>
