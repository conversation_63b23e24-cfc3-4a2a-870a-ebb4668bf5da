<?php
include('header.php');
include('navbar.php');

// التحقق من وجود بيانات الحجز المكتمل
if (!isset($_SESSION['completed_booking'])) {
    header('Location: bus-booking.php');
    exit;
}

$booking = $_SESSION['completed_booking'];
$trip = $booking['trip_data'];
?>

<style>
.confirmation-section {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    min-height: 100vh;
    padding: 60px 0;
    position: relative;
}

.confirmation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.confirmation-container {
    position: relative;
    z-index: 2;
    max-width: 700px;
    margin: 0 auto;
}

.success-icon {
    text-align: center;
    margin-bottom: 30px;
}

.success-icon i {
    font-size: 5rem;
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    padding: 30px;
    border: 3px solid white;
}

.confirmation-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.booking-reference {
    background: #f8f9fa;
    border: 2px dashed #28a745;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 30px;
}

.reference-number {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
    font-family: 'Courier New', monospace;
}

.booking-details {
    border-top: 2px solid #e9ecef;
    padding-top: 25px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-label {
    font-weight: 600;
    color: #495057;
}

.detail-value {
    color: #212529;
    text-align: left;
}

.route-display {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.route-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.route-point {
    text-align: center;
    flex: 1;
}

.route-arrow {
    color: #28a745;
    font-size: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn-print {
    background: #6c757d;
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-print:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-new-booking {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-new-booking:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

.important-notes {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.important-notes h6 {
    color: #856404;
    margin-bottom: 15px;
}

.important-notes ul {
    margin: 0;
    padding-right: 20px;
    color: #856404;
}

@media (max-width: 768px) {
    .confirmation-container {
        margin: 0 15px;
    }
    
    .confirmation-card {
        padding: 25px;
    }
    
    .route-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .route-arrow {
        transform: rotate(90deg);
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media print {
    .action-buttons, .navbar, .footer {
        display: none !important;
    }
    
    .confirmation-section {
        background: white !important;
        padding: 20px 0 !important;
    }
    
    .confirmation-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
</style>

<section class="confirmation-section">
    <div class="container">
        <div class="confirmation-container">
            <!-- أيقونة النجاح -->
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>

            <!-- بطاقة التأكيد -->
            <div class="confirmation-card">
                <div class="text-center mb-4">
                    <h2 class="text-success mb-2">تم تأكيد حجزك بنجاح!</h2>
                    <p class="text-muted">شكراً لك على اختيار خدماتنا</p>
                </div>

                <!-- الرقم المرجعي -->
                <div class="booking-reference">
                    <h5 class="mb-2">رقم الحجز المرجعي</h5>
                    <div class="reference-number"><?= $booking['booking_reference'] ?></div>
                    <small class="text-muted">احتفظ بهذا الرقم للمراجعة</small>
                </div>

                <!-- تفاصيل الرحلة -->
                <div class="route-display">
                    <h6 class="mb-3">تفاصيل الرحلة</h6>
                    <div class="route-info">
                        <div class="route-point">
                            <div class="fw-bold"><?= $trip['departure_station_name'] ?></div>
                            <div class="text-muted small"><?= $trip['departure_city_name'] ?></div>
                            <div class="text-primary"><?= date('H:i', strtotime($trip['departure_time'])) ?></div>
                        </div>
                        <div class="route-arrow">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="route-point">
                            <div class="fw-bold"><?= $trip['arrival_station_name'] ?></div>
                            <div class="text-muted small"><?= $trip['arrival_city_name'] ?></div>
                            <div class="text-primary"><?= date('H:i', strtotime($trip['arrival_time'])) ?></div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الحجز -->
                <div class="booking-details">
                    <h6 class="mb-3">تفاصيل الحجز</h6>
                    
                    <div class="detail-row">
                        <span class="detail-label">اسم المسافر:</span>
                        <span class="detail-value"><?= $booking['passenger_name'] ?></span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">رقم الجوال:</span>
                        <span class="detail-value"><?= $booking['passenger_phone'] ?></span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">تاريخ السفر:</span>
                        <span class="detail-value"><?= date('d/m/Y', strtotime($booking['travel_date'])) ?></span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">عدد المسافرين:</span>
                        <span class="detail-value"><?= $booking['seats_count'] ?> <?= $booking['seats_count'] == 1 ? 'مسافر' : 'مسافرين' ?></span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">المبلغ الإجمالي:</span>
                        <span class="detail-value text-success fw-bold"><?= number_format($booking['total_price'], 2) ?> ريال</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">حالة الحجز:</span>
                        <span class="detail-value">
                            <span class="badge bg-warning">في انتظار التأكيد</span>
                        </span>
                    </div>
                </div>

                <!-- ملاحظات مهمة -->
                <div class="important-notes">
                    <h6><i class="fas fa-exclamation-triangle"></i> ملاحظات مهمة:</h6>
                    <ul>
                        <li>يرجى الوصول إلى المحطة قبل موعد الانطلاق بـ 30 دقيقة على الأقل</li>
                        <li>احضر معك هوية شخصية سارية المفعول</li>
                        <li>سيتم التواصل معك لتأكيد الحجز خلال 24 ساعة</li>
                        <li>في حالة الإلغاء، يرجى التواصل معنا قبل 24 ساعة من موعد السفر</li>
                    </ul>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="action-buttons">
                    <button onclick="window.print()" class="btn btn-print">
                        <i class="fas fa-print"></i> طباعة التذكرة
                    </button>
                    <a href="bus-booking.php" class="btn btn-new-booking">
                        <i class="fas fa-plus"></i> حجز جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// مسح بيانات الحجز من الجلسة بعد عرض الصفحة
setTimeout(function() {
    fetch('clear-booking-session.php', {
        method: 'POST'
    });
}, 5000);
</script>

<?php include('footer.php'); ?>
