<?php
include('header.php');
include('navbar.php');

// جلب المدن والمحطات للباصات
$bus_cities = getAllFrom('DISTINCT city_id, city_name', 'bus_stations', 'WHERE status = 1', 'ORDER BY city_name');
?>

<style>
.bus-hero-section {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.bus-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: white;
    font-size: 3.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.3rem;
    margin-bottom: 40px;
}

.booking-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    margin: 0 auto;
}

.trip-type-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 5px;
}

.trip-type-tab {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #495057;
}

.trip-type-tab.active {
    background: #ff8c00;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.3);
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
}

.form-control:focus, .form-select:focus {
    border-color: #ff8c00;
    box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
    outline: none;
}

.search-btn {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
    border: none;
    color: white;
    padding: 15px 40px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 140, 0, 0.4);
    background: linear-gradient(135deg, #ff6b00 0%, #ff5500 100%);
}

.bus-image {
    position: absolute;
    right: -50px;
    bottom: 0;
    width: 700px;
    opacity: 0.8;
    z-index: 1;
}

.features-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.feature-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 3rem;
    color: #ff8c00;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .booking-card {
        padding: 25px;
        margin: 20px;
    }
    
    .bus-image {
        display: none;
    }
    
    .trip-type-tabs {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<section class="bus-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">باصات من وإلى مكة يوميا</h1>
                    <p class="hero-subtitle">رحلات مريحة وآمنة بأسعار مناسبة</p>
                    
                    <div class="booking-card">
                        <form id="busSearchForm" method="POST" action="bus-search-results.php">
                            <!-- تبويبات نوع الرحلة -->
                            <div class="trip-type-tabs">
                                <button type="button" class="trip-type-tab active" data-type="to_makkah">
                                    <i class="fas fa-arrow-left"></i>
                                    ذهاب إلى مكة
                                </button>
                                <button type="button" class="trip-type-tab" data-type="from_makkah">
                                    <i class="fas fa-arrow-right"></i>
                                    عودة من مكة
                                </button>
                            </div>
                            
                            <input type="hidden" name="trip_type" id="trip_type" value="to_makkah">

                            <!-- المدينة -->
                            <div class="form-group">
                                <label class="form-label">السفر من المدينة</label>
                                <select class="form-select" id="city_select" name="city_id" required>
                                    <option value="">اختر المدينة</option>
                                    <?php foreach($bus_cities as $city): ?>
                                        <option value="<?= $city['city_id'] ?>"><?= $city['city_name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- المحطة -->
                            <div class="form-group">
                                <label class="form-label">السفر من المحطة</label>
                                <select class="form-select" id="station_select" name="station_id" required>
                                    <option value="">اختر المحطة</option>
                                </select>
                            </div>

                            <div class="row">
                                <!-- تاريخ السفر -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">تاريخ السفر</label>
                                        <input type="date" class="form-control" name="travel_date" 
                                               min="<?= date('Y-m-d') ?>" 
                                               max="<?= date('Y-m-d', strtotime('+3 months')) ?>" required>
                                    </div>
                                </div>

                                <!-- عدد المسافرين -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">عدد المسافرين</label>
                                        <select class="form-select" name="seats_count" required>
                                            <?php for($i = 1; $i <= 10; $i++): ?>
                                                <option value="<?= $i ?>"><?= $i ?> <?= $i == 1 ? 'مسافر' : 'مسافرين' ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                                أعرض الرحلات
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="img/bus-1.jpeg" alt="Bus" class="bus-image">
            </div>
        </div>
    </div>
</section>

<!-- قسم المميزات -->
<section class="features-section">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="mb-3">لماذا تختار خدماتنا؟</h2>
                <p class="text-muted">نوفر لك أفضل تجربة سفر مريحة وآمنة</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5>رحلات آمنة</h5>
                    <p class="text-muted">باصات حديثة مع أعلى معايير الأمان والسلامة</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5>مواعيد دقيقة</h5>
                    <p class="text-muted">التزام تام بمواعيد الانطلاق والوصول</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h5>أسعار مناسبة</h5>
                    <p class="text-muted">أفضل الأسعار مع خدمة عالية الجودة</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// تبديل نوع الرحلة
document.querySelectorAll('.trip-type-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.trip-type-tab').forEach(t => t.classList.remove('active'));
        
        // إضافة الفئة النشطة للتبويب المحدد
        this.classList.add('active');
        
        // تحديث قيمة نوع الرحلة
        document.getElementById('trip_type').value = this.dataset.type;
        
        // إعادة تحميل المحطات
        const citySelect = document.getElementById('city_select');
        if (citySelect.value) {
            citySelect.dispatchEvent(new Event('change'));
        }
    });
});

// تحديث المحطات عند تغيير المدينة
document.getElementById('city_select').addEventListener('change', function() {
    const cityId = this.value;
    const stationSelect = document.getElementById('station_select');
    const tripType = document.getElementById('trip_type').value;
    
    // مسح المحطات الحالية
    stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
    
    if (cityId) {
        // جلب المحطات للمدينة المحددة
        fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_stations&city_id=${cityId}&trip_type=${tripType}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.stations.forEach(station => {
                    const option = document.createElement('option');
                    option.value = station.id;
                    option.textContent = station.station_name;
                    stationSelect.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error:', error));
    }
});
</script>

<?php include('footer.php'); ?>
