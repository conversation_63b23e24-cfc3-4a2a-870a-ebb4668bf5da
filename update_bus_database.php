<?php
require_once 'config.php';

echo "<h2>تحديث قاعدة البيانات - نظام حجز الباصات</h2>";

try {
    // إنشاء جدول محطات الباصات
    echo "<h3>إنشاء جدول محطات الباصات...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `bus_stations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `station_name` varchar(255) NOT NULL,
        `city_id` int(11) NOT NULL,
        `city_name` varchar(255) NOT NULL,
        `address` text DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `coordinates` varchar(100) DEFAULT NULL,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_city_id` (`city_id`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✓ تم إنشاء جدول محطات الباصات بنجاح</p>";

    // إدراج بيانات المحطات
    echo "<h3>إدراج بيانات المحطات...</h3>";
    $stations = [
        [1, 'محطة الرياض المركزية', 1, 'الرياض', 'شارع الملك فهد، الرياض', '0112345678', '24.7136,46.6753'],
        [2, 'محطة الدمام الرئيسية', 2, 'الدمام', 'شارع الملك عبدالعزيز، الدمام', '0138765432', '26.4207,50.0888'],
        [3, 'محطة جدة المركزية', 3, 'جدة', 'شارع المدينة المنورة، جدة', '0126543210', '21.4858,39.1925'],
        [4, 'محطة المدينة المنورة', 4, 'المدينة المنورة', 'شارع قباء، المدينة المنورة', '0148765432', '24.5247,39.5692'],
        [5, 'محطة مكة المكرمة - العزيزية', 5, 'مكة المكرمة', 'حي العزيزية، مكة المكرمة', '0125432109', '21.3891,39.8579'],
        [6, 'محطة مكة المكرمة - الشرائع', 5, 'مكة المكرمة', 'حي الشرائع، مكة المكرمة', '0125432108', '21.3891,39.8579']
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO `bus_stations` (`id`, `station_name`, `city_id`, `city_name`, `address`, `phone`, `coordinates`) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($stations as $station) {
        $stmt->execute($station);
    }
    echo "<p style='color: green;'>✓ تم إدراج بيانات المحطات بنجاح</p>";

    // إنشاء جدول رحلات الباصات
    echo "<h3>إنشاء جدول رحلات الباصات...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `bus_trips` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_type` enum('to_makkah','from_makkah') NOT NULL COMMENT 'نوع الرحلة: ذهاب إلى مكة أو عودة من مكة',
        `departure_station_id` int(11) NOT NULL,
        `arrival_station_id` int(11) NOT NULL,
        `departure_time` time NOT NULL,
        `arrival_time` time NOT NULL,
        `seat_price` decimal(8,2) NOT NULL,
        `total_seats` int(11) NOT NULL DEFAULT 50,
        `available_days` varchar(20) NOT NULL DEFAULT '1,2,3,4,5,6,7' COMMENT 'أيام الأسبوع المتاحة (1=الأحد, 7=السبت)',
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `fk_departure_station` (`departure_station_id`),
        KEY `fk_arrival_station` (`arrival_station_id`),
        KEY `idx_trip_type` (`trip_type`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✓ تم إنشاء جدول رحلات الباصات بنجاح</p>";

    // إضافة القيود الخارجية لجدول الرحلات
    try {
        $db->exec("ALTER TABLE `bus_trips` ADD CONSTRAINT `fk_departure_station` FOREIGN KEY (`departure_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE");
        $db->exec("ALTER TABLE `bus_trips` ADD CONSTRAINT `fk_arrival_station` FOREIGN KEY (`arrival_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE");
        echo "<p style='color: green;'>✓ تم إضافة القيود الخارجية لجدول الرحلات</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ القيود الخارجية موجودة مسبقاً</p>";
    }

    // إدراج بيانات الرحلات
    echo "<h3>إدراج بيانات الرحلات...</h3>";
    $trips = [
        // رحلات الذهاب إلى مكة
        ['to_makkah', 1, 5, '06:00:00', '12:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 1, 5, '14:00:00', '20:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 1, 6, '08:00:00', '14:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 2, 5, '07:00:00', '14:00:00', 200.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 2, 6, '15:00:00', '22:00:00', 200.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 3, 5, '05:00:00', '07:00:00', 80.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 3, 6, '13:00:00', '15:00:00', 80.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 4, 5, '09:00:00', '13:00:00', 120.00, 50, '1,2,3,4,5,6,7', 1],
        ['to_makkah', 4, 6, '17:00:00', '21:00:00', 120.00, 50, '1,2,3,4,5,6,7', 1],
        // رحلات العودة من مكة
        ['from_makkah', 5, 1, '08:00:00', '14:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 5, 1, '16:00:00', '22:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 6, 1, '10:00:00', '16:00:00', 150.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 5, 2, '09:00:00', '16:00:00', 200.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 6, 2, '17:00:00', '24:00:00', 200.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 5, 3, '07:00:00', '09:00:00', 80.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 6, 3, '15:00:00', '17:00:00', 80.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 5, 4, '11:00:00', '15:00:00', 120.00, 50, '1,2,3,4,5,6,7', 1],
        ['from_makkah', 6, 4, '19:00:00', '23:00:00', 120.00, 50, '1,2,3,4,5,6,7', 1]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO `bus_trips` (`trip_type`, `departure_station_id`, `arrival_station_id`, `departure_time`, `arrival_time`, `seat_price`, `total_seats`, `available_days`, `status`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($trips as $trip) {
        $stmt->execute($trip);
    }
    echo "<p style='color: green;'>✓ تم إدراج بيانات الرحلات بنجاح</p>";

    // إنشاء جدول حجوزات الباصات
    echo "<h3>إنشاء جدول حجوزات الباصات...</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `bus_bookings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `booking_reference` varchar(20) NOT NULL,
        `trip_id` int(11) NOT NULL,
        `trip_date` date NOT NULL,
        `passenger_name` varchar(255) NOT NULL,
        `passenger_phone` varchar(20) NOT NULL,
        `passenger_email` varchar(255) DEFAULT NULL,
        `passenger_id_number` varchar(20) DEFAULT NULL,
        `seats_count` int(11) NOT NULL DEFAULT 1,
        `total_price` decimal(10,2) NOT NULL,
        `booking_status` enum('pending','confirmed','cancelled','completed') NOT NULL DEFAULT 'pending',
        `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending',
        `special_requests` text DEFAULT NULL,
        `booking_date` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        `created_by_ip` varchar(45) DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `booking_reference` (`booking_reference`),
        KEY `fk_booking_trip` (`trip_id`),
        KEY `idx_trip_date` (`trip_date`),
        KEY `idx_booking_status` (`booking_status`),
        KEY `idx_passenger_phone` (`passenger_phone`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->exec($sql);
    echo "<p style='color: green;'>✓ تم إنشاء جدول حجوزات الباصات بنجاح</p>";

    // إضافة القيود الخارجية لجدول الحجوزات
    try {
        $db->exec("ALTER TABLE `bus_bookings` ADD CONSTRAINT `fk_booking_trip` FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE");
        echo "<p style='color: green;'>✓ تم إضافة القيود الخارجية لجدول الحجوزات</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ القيود الخارجية موجودة مسبقاً</p>";
    }

    echo "<h2 style='color: green;'>✅ تم تحديث قاعدة البيانات بنجاح!</h2>";
    echo "<p>يمكنك الآن استخدام نظام حجز الباصات.</p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
