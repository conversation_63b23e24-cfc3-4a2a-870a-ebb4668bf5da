<?php
include('webset.php');
include('session.php');
//-------------------------------------------------------------------------------
//-------------------------------------------------------------------------------

// جلب المحطات حسب المدينة ونوع الرحلة
if(isset($_POST['action']) && $_POST['action'] == 'get_stations'){
    $city_id = intval($_POST['city_id']);
    $trip_type = $_POST['trip_type'];

    try {
        if($trip_type == 'to_makkah') {
            // للذهاب إلى مكة - جلب المحطات من المدن الأخرى (ليس مكة)
            $stations = getAllFrom('*', 'bus_stations', 'WHERE city_id = '.$city_id.' AND city_name != "مكة المكرمة" AND status = 1', 'ORDER BY station_name');
        } else {
            // للعودة من مكة - جلب محطات مكة فقط
            $stations = getAllFrom('*', 'bus_stations', 'WHERE city_name = "مكة المكرمة" AND status = 1', 'ORDER BY station_name');
        }

        echo json_encode([
            'success' => true,
            'stations' => $stations
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit;
    }
}

//-------------------------------------------------------------------------------
//-------------------------------------------------------------------------------
if(isset($_POST['do']) && $_POST['do'] == 'ConfirmBookTrip'){
    if($_POST['bt_name'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل الأسم بالكامل.');
    }else if($_POST['bt_phone'] == ''){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم الجوال.');
    }elseif(strlen($_POST['bt_phone']) < 8){
        echo Show_Alert('danger' , 'من فضلك أدخل رقم جوال صحيح.');
    }else{

            $stmt = $db->prepare("INSERT INTO orders ( name , phone , hamla_title , hamla_id , offer_title , offer_id , people , datee , madina , payment , room_title , return_date , total_price , discount ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8 ,:user_9 ,:user_10 ,:user_11 ,:user_12 ,:user_13 ,:user_14 )");  
            $stmt->execute(array(
                'user_1' => $_POST['bt_name'] ,
                'user_2' => $_POST['bt_phone'] ,
                'user_3' => $_POST['hamla'] ,
                'user_4' => $_POST['id'] ,
                'user_5' => $_POST['title'] ,
                'user_6' => $_POST['bt_book'] ,
                'user_7' => $_POST['bt_people'] ,
                'user_8' => $_POST['date_timepicker_end'] ,
                'user_9' => $_POST['bt_mvist'] ,
                'user_10' => $_POST['selectedPayment'] ,
                'user_11' => $_POST['room_title'] ,
                'user_12' => $_POST['return_date_display'] ,
                'user_13' => $_POST['total_price'] ,
                'user_14' => $_POST['discount_display'] 
            ));
            echo 'done'; 

            $_SESSION['Omra'] = [
                'bt_name' => $_POST['bt_name'] ,
                'bt_phone' => $_POST['bt_phone'] ,
                'hamla' => $_POST['hamla'] , 
                'id' => $_POST['id'] , 
                'title' => $_POST['title'] , 
                'bt_book' => $_POST['bt_book'] , 
                'bt_people' => $_POST['bt_people'] , 
                'date_timepicker_end' => $_POST['date_timepicker_end'] , 
                'bt_mvist' => $_POST['bt_mvist'] , 
                'selectedPayment' => $_POST['selectedPayment'] , 
                'room_title' => $_POST['room_title'] , 
                
                'return_date_display' => $_POST['return_date_display'] , 
                'total_price' => $_POST['total_price'] , 
                'discount_display' => $_POST['discount_display'] , 
            ];


            $name  = $_POST['bt_name']  ?? '---';
            $phone =  $_POST['bt_phone'] ?? '---';
            $trip  = $_POST['title']  ?? '---';

            // الرسالة اللي هتتبعت
            $message = "تم تأكيد الحجز بنجاح:\n";
            $message .= "الاسم: $name\n";
            $message .= "الجوال: $phone\n";
            $message .= "الرحلة: ".$_POST['hamla'].PHP_EOL;
            $message .= "الغرفه: ".$_POST['room_title'].PHP_EOL;
            $message .= "تاريخ الرحله: ".$_POST['date_timepicker_end'].PHP_EOL;
            $message .= "التكلفه: ".$_POST['total_price'].PHP_EOL;



            $telegram_token = "**********************************************";
            $telegram_channel = "-4800733652";
        
            $data = [
                'chat_id' => $telegram_channel,
                'text' => $message 
            ];
            // 'teeeee' . PHP_EOL . PHP_EOL . 'test',
            try {
            $ch = curl_init("https://api.telegram.org/bot$telegram_token/sendMessage");
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $result = curl_exec($ch);
            curl_close($ch);
            } catch (Exception $e) {} 

    }
//-------------------------------------------------------------------------------
    
}